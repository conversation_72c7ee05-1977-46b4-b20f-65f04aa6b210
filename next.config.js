/** @type {import('next').NextConfig} */
const nextConfig = {
  output: "export",
  eslint: {
    ignoreDuringBuilds: true,
  },
  images: {
    unoptimized: true,
    formats: ["image/webp", "image/avif"],
    deviceSizes: [640, 750, 828, 1080, 1200, 1920, 2048, 3840],
    imageSizes: [16, 32, 48, 64, 96, 128, 256, 384],
    minimumCacheTTL: 60 * 60 * 24 * 365, // 1 year
  },
  experimental: {
    optimizeCss: true,
    optimizePackageImports: ["lucide-react", "gsap"],
  },
  compiler: {
    removeConsole: process.env.NODE_ENV === "production",
  },
  webpack: (config, { dev, isServer }) => {
    // Optimize for production
    if (!dev) {
      config.optimization = {
        ...config.optimization,
        splitChunks: {
          chunks: "all",
          cacheGroups: {
            vendor: {
              test: /[\\/]node_modules[\\/]/,
              name: "vendors",
              chunks: "all",
            },
            gsap: {
              test: /[\\/]node_modules[\\/]gsap[\\/]/,
              name: "gsap",
              chunks: "async",
            },
          },
        },
      };
    }

    // Disable cache in development for hot reloading
    if (dev && !isServer) {
      config.cache = false;
    }

    return config;
  },
  // Enable compression
  compress: true,
  // Optimize fonts
  optimizeFonts: true,
  // Enable SWC minification
  swcMinify: true,
  // Generate source maps for production debugging
  productionBrowserSourceMaps: false,
  // Optimize CSS
  experimental: {
    optimizeCss: true,
  },
};

module.exports = nextConfig;
