import Header from "@/components/layout/Header";
import Footer from "@/components/layout/Footer";
import HeroSection from "@/components/sections/HeroSection";
import AboutSection from "@/components/sections/AboutSection";
import GallerySection from "@/components/sections/GallerySection";
import ServicesSection from "@/components/sections/ServicesSection";
import TestimonialsSection from "@/components/sections/TestimonialsSection";
import ContactSection from "@/components/sections/ContactSection";
import dynamic from "next/dynamic";

// Lazy load non-critical components
const AnimationObserver = dynamic(
  () => import("@/components/ui/AnimationObserver"),
  {
    ssr: false,
  }
);
const WhatsAppButton = dynamic(() => import("@/components/ui/WhatsAppButton"), {
  ssr: false,
});

export default function Home() {
  return (
    <div className="flex min-h-screen flex-col">
      <Header />
      <main role="main" aria-label="Main content">
        <HeroSection />
        <AboutSection />
        <GallerySection />
        <ServicesSection />
        <TestimonialsSection />
        <ContactSection />
      </main>
      <WhatsAppButton />
      <Footer />
      <AnimationObserver>
        <div></div>
      </AnimationObserver>
    </div>
  );
}
