@tailwind base;
@tailwind components;
@tailwind utilities;

:root {
  --foreground-rgb: 51, 51, 51;
  --background-rgb: 250, 248, 246;

  --primary: 30, 24, 16;
  --primary-foreground: 250, 250, 249;

  --secondary: 22, 78, 99;
  --secondary-foreground: 250, 250, 249;

  --accent: 146, 76, 64;
  --accent-foreground: 250, 250, 249;

  --muted: 245, 237, 230;
  --muted-foreground: 115, 115, 115;

  --card: 255, 255, 255;
  --card-foreground: 51, 51, 51;

  --border: 230, 225, 220;
  --input: 230, 225, 220;

  --gold: 183, 161, 121;
  --brown: 101, 67, 53;
  --cream: 250, 248, 246;
}

@layer base {
  * {
    box-sizing: border-box;
  }

  html {
    scroll-behavior: smooth;
    -webkit-text-size-adjust: 100%;
    text-size-adjust: 100%;
  }

  body {
    @apply bg-background text-foreground antialiased;
    font-feature-settings: "rlig" 1, "calt" 1;
    line-height: 1.6;
    -webkit-font-smoothing: antialiased;
    -moz-osx-font-smoothing: grayscale;
    text-rendering: optimizeLegibility;
  }

  h1,
  h2,
  h3,
  h4,
  h5,
  h6 {
    @apply font-cormorant font-medium tracking-tight;
    line-height: 1.2;
  }

  p,
  span,
  li,
  a,
  button {
    @apply font-montserrat;
  }

  img {
    max-width: 100%;
    height: auto;
  }

  /* Focus styles for accessibility */
  :focus-visible {
    outline: 2px solid rgb(183, 161, 121);
    outline-offset: 2px;
  }

  /* Reduce motion for users who prefer it */
  @media (prefers-reduced-motion: reduce) {
    *,
    *::before,
    *::after {
      animation-duration: 0.01ms !important;
      animation-iteration-count: 1 !important;
      transition-duration: 0.01ms !important;
      scroll-behavior: auto !important;
    }
  }
}

.font-cormorant {
  font-family: var(--font-cormorant);
}

.font-montserrat {
  font-family: var(--font-montserrat);
}

.container {
  @apply px-4 md:px-6 lg:px-8 mx-auto max-w-7xl;
}

.section-padding {
  @apply py-16 md:py-24;
}

.btn {
  @apply rounded-none border border-primary/80 px-6 py-3 text-sm uppercase tracking-widest transition-all duration-300 font-montserrat font-medium inline-flex items-center justify-center;
}

.btn-primary {
  @apply bg-primary text-white hover:bg-primary/90 border-primary;
}

.btn-outline {
  @apply bg-transparent text-primary hover:bg-primary/5 border-primary/20;
}

.section-title {
  @apply text-4xl md:text-5xl font-cormorant font-medium tracking-tight relative mb-4 md:mb-8;
}

.section-title::after {
  content: "";
  @apply absolute -bottom-2 left-0 w-16 h-[2px] bg-gold;
}

.section-title.centered::after {
  @apply left-1/2 -translate-x-1/2;
}

.gold-underline {
  @apply relative inline-block;
}

.gold-underline::after {
  content: "";
  @apply absolute -bottom-2 left-0 w-16 h-[2px] bg-gold transition-all duration-300;
}

.gold-underline:hover::after {
  @apply w-full;
}

.animate-on-scroll {
  opacity: 0;
  transform: translateY(20px);
  transition: opacity 0.6s ease-out, transform 0.6s ease-out;
  will-change: opacity, transform;
}

.animate-on-scroll.is-visible {
  opacity: 1;
  transform: translateY(0);
  will-change: auto;
}

/* Gallery item animations */
.gallery-item {
  overflow: hidden;
  position: relative;
  will-change: transform;
}

/* Image hover effects are now handled via Tailwind classes */

.gallery-overlay {
  @apply absolute inset-0 bg-primary/40 opacity-0 transition-opacity duration-300 flex items-center justify-center;
  will-change: opacity;
}

.gallery-item:hover .gallery-overlay {
  opacity: 1;
}

/* Performance optimizations */
.gallery-item,
.animate-on-scroll {
  transform: translateZ(0); /* Force hardware acceleration */
  backface-visibility: hidden;
}

@layer components {
  .mehendi-pattern {
    position: relative;
  }

  .mehendi-pattern::before {
    content: "";
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background-image: radial-gradient(
        circle at 25% 25%,
        rgba(183, 161, 121, 0.1) 2px,
        transparent 2px
      ),
      radial-gradient(
        circle at 75% 75%,
        rgba(183, 161, 121, 0.08) 1px,
        transparent 1px
      ),
      radial-gradient(
        circle at 50% 50%,
        rgba(183, 161, 121, 0.06) 1.5px,
        transparent 1.5px
      );
    background-size: 60px 60px, 40px 40px, 80px 80px;
    background-position: 0 0, 20px 20px, 40px 40px;
    opacity: 0.3;
    pointer-events: none;
  }
}
