import "./globals.css";
import type { Metadata } from "next";
import { <PERSON><PERSON><PERSON><PERSON>_<PERSON>, <PERSON><PERSON><PERSON> } from "next/font/google";
import { ThemeProvider } from "@/components/theme-provider";

const cormorant = Cormoran<PERSON>_<PERSON>aramond({
  subsets: ["latin"],
  weight: ["300", "400", "500", "600", "700"],
  variable: "--font-cormorant",
  display: "swap",
  preload: true,
});

const montserrat = Montserrat({
  subsets: ["latin"],
  weight: ["300", "400", "500", "600"],
  variable: "--font-montserrat",
  display: "swap",
  preload: true,
});

// Enhanced SEO metadata
export const metadata: Metadata = {
  title: {
    default: "Manya Mehendi Creations | Professional Henna Artist in Delhi NCR",
    template: "%s | Manya Mehendi Creations",
  },
  description:
    "Professional mehendi artist in Delhi NCR specializing in bridal henna, wedding mehendi, and intricate henna designs. Book your appointment for exquisite mehendi artistry.",
  keywords: [
    "mehendi artist Delhi",
    "bridal mehendi",
    "henna designs",
    "wedding mehendi",
    "mehendi artist NCR",
    "professional henna artist",
    "intricate mehendi designs",
    "bridal henna",
    "mehendi booking Delhi",
  ],
  authors: [{ name: "Manya Mehendi Creations" }],
  creator: "Manya Mehendi Creations",
  publisher: "Manya Mehendi Creations",
  formatDetection: {
    email: false,
    address: false,
    telephone: false,
  },
  metadataBase: new URL("https://manyamehendicreations.com"),
  alternates: {
    canonical: "/",
  },
  openGraph: {
    title: "Manya Mehendi Creations | Professional Henna Artist in Delhi NCR",
    description:
      "Professional mehendi artist in Delhi NCR specializing in bridal henna, wedding mehendi, and intricate henna designs. Book your appointment for exquisite mehendi artistry.",
    url: "https://manyamehendicreations.com",
    siteName: "Manya Mehendi Creations",
    images: [
      {
        url: "/images/og-image.jpg",
        width: 1200,
        height: 630,
        alt: "Manya Mehendi Creations - Professional Henna Artist",
      },
    ],
    locale: "en_IN",
    type: "website",
  },
  twitter: {
    card: "summary_large_image",
    title: "Manya Mehendi Creations | Professional Henna Artist in Delhi NCR",
    description:
      "Professional mehendi artist in Delhi NCR specializing in bridal henna, wedding mehendi, and intricate henna designs.",
    images: ["/images/twitter-image.jpg"],
    creator: "@manyamehendi",
  },
  robots: {
    index: true,
    follow: true,
    googleBot: {
      index: true,
      follow: true,
      "max-video-preview": -1,
      "max-image-preview": "large",
      "max-snippet": -1,
    },
  },
  verification: {
    google: "your-google-verification-code",
    yandex: "your-yandex-verification-code",
    yahoo: "your-yahoo-verification-code",
  },
};

export default function RootLayout({
  children,
}: {
  children: React.ReactNode;
}) {
  return (
    <html lang="en" suppressHydrationWarning>
      <head>
        <meta
          name="viewport"
          content="width=device-width, initial-scale=1, maximum-scale=5, user-scalable=yes"
        />
        <meta name="theme-color" content="#1E1810" />
        <meta name="color-scheme" content="light" />
        <link rel="preconnect" href="https://fonts.googleapis.com" />
        <link
          rel="preconnect"
          href="https://fonts.gstatic.com"
          crossOrigin="anonymous"
        />
        <link rel="dns-prefetch" href="https://images.unsplash.com" />
        <link rel="dns-prefetch" href="https://images.pexels.com" />
        <meta name="theme-color" content="#1E1810" />
        <meta name="apple-mobile-web-app-capable" content="yes" />
        <meta name="apple-mobile-web-app-status-bar-style" content="default" />
        <meta name="apple-mobile-web-app-title" content="Manya Mehendi" />
        <meta name="mobile-web-app-capable" content="yes" />

        {/* Critical CSS for above-the-fold content */}
        <style
          dangerouslySetInnerHTML={{
            __html: `
            .hero-critical {
              height: 100vh;
              display: flex;
              align-items: center;
              justify-content: center;
              position: relative;
              overflow: hidden;
            }
            .hero-bg {
              position: absolute;
              inset: 0;
              z-index: 0;
            }
            .hero-content {
              position: relative;
              z-index: 10;
              text-align: center;
              padding: 0 1rem;
            }
            .hero-title {
              color: white;
              font-size: 2rem;
              margin-bottom: 1.5rem;
              font-weight: 500;
              text-shadow: 0 25px 50px -12px rgba(0, 0, 0, 0.25);
            }
            @media (min-width: 768px) {
              .hero-title {
                font-size: 3rem;
              }
            }
            @media (min-width: 1024px) {
              .hero-title {
                font-size: 4.5rem;
              }
            }
          `,
          }}
        />
        <script
          type="application/ld+json"
          dangerouslySetInnerHTML={{
            __html: JSON.stringify({
              "@context": "https://schema.org",
              "@type": "LocalBusiness",
              name: "Manya Mehendi Creations",
              description:
                "Professional mehendi artist in Delhi NCR specializing in bridal henna, wedding mehendi, and intricate henna designs.",
              url: "https://manyamehendicreations.com",
              telephone: "+91-98765-43210",
              email: "<EMAIL>",
              address: {
                "@type": "PostalAddress",
                addressLocality: "Delhi",
                addressRegion: "NCR",
                addressCountry: "IN",
              },
              geo: {
                "@type": "GeoCoordinates",
                latitude: "28.6139",
                longitude: "77.2090",
              },
              openingHours: ["Mo-Sa 10:00-20:00", "Su by appointment"],
              priceRange: "$$",
              serviceArea: {
                "@type": "GeoCircle",
                geoMidpoint: {
                  "@type": "GeoCoordinates",
                  latitude: "28.6139",
                  longitude: "77.2090",
                },
                geoRadius: "50000",
              },
              hasOfferCatalog: {
                "@type": "OfferCatalog",
                name: "Mehendi Services",
                itemListElement: [
                  {
                    "@type": "Offer",
                    itemOffered: {
                      "@type": "Service",
                      name: "Bridal Mehendi",
                      description:
                        "Intricate bridal henna designs for weddings",
                    },
                  },
                  {
                    "@type": "Offer",
                    itemOffered: {
                      "@type": "Service",
                      name: "Party Mehendi",
                      description:
                        "Beautiful henna designs for special occasions",
                    },
                  },
                  {
                    "@type": "Offer",
                    itemOffered: {
                      "@type": "Service",
                      name: "Simple Mehendi",
                      description: "Elegant simple henna patterns",
                    },
                  },
                ],
              },
              sameAs: [
                "https://www.instagram.com/manyamehendicreations",
                "https://www.facebook.com/manyamehendicreations",
              ],
            }),
          }}
        />
      </head>
      <body
        className={`${cormorant.variable} ${montserrat.variable} font-sans`}
      >
        <ThemeProvider
          attribute="class"
          defaultTheme="light"
          enableSystem
          disableTransitionOnChange
        >
          {children}
        </ThemeProvider>
      </body>
    </html>
  );
}
