"use client";

import { useState, useEffect, useRef } from "react";
import Image from "next/image";
import { ChevronLeft, ChevronRight, Star } from "lucide-react";

interface Testimonial {
  id: number;
  name: string;
  role: string;
  content: string;
  rating: number;
  image: string;
}

const testimonials: Testimonial[] = [
  {
    id: 1,
    name: "<PERSON><PERSON>",
    role: "Bride",
    content:
      "<PERSON><PERSON> created the most beautiful bridal mehendi for my wedding. The designs were intricate and perfectly suited my style. Everyone couldn't stop complimenting how gorgeous they looked!",
    rating: 5,
    image:
      "https://images.pexels.com/photos/3812944/pexels-photo-3812944.jpeg?auto=compress&cs=tinysrgb&w=400&h=400&dpr=2",
  },
  {
    id: 2,
    name: "<PERSON><PERSON>",
    role: "Client",
    content:
      "I booked <PERSON><PERSON> for my sister's engagement ceremony and was amazed by her talent. The designs were unique and she was so patient throughout the process. Highly recommend!",
    rating: 5,
    image:
      "https://images.pexels.com/photos/1239291/pexels-photo-1239291.jpeg?auto=compress&cs=tinysrgb&w=400&h=400&dpr=2",
  },
  {
    id: 3,
    name: "<PERSON><PERSON>",
    role: "Birthday Girl",
    content:
      "Got mehendi done for my 25th birthday celebration and couldn't be happier! Manya's designs are a perfect blend of traditional and modern aesthetics. Will definitely be booking again.",
    rating: 5,
    image:
      "https://images.pexels.com/photos/1181686/pexels-photo-1181686.jpeg?auto=compress&cs=tinysrgb&w=400&h=400&dpr=2",
  },
];

export default function TestimonialsSection() {
  const [currentSlide, setCurrentSlide] = useState(0);
  const [isAnimating, setIsAnimating] = useState(false);
  const slideRef = useRef<HTMLDivElement>(null);

  const goToPrevSlide = () => {
    if (isAnimating) return;

    setIsAnimating(true);
    setCurrentSlide((prev) =>
      prev === 0 ? testimonials.length - 1 : prev - 1
    );

    setTimeout(() => {
      setIsAnimating(false);
    }, 500);
  };

  const goToNextSlide = () => {
    if (isAnimating) return;

    setIsAnimating(true);
    setCurrentSlide((prev) =>
      prev === testimonials.length - 1 ? 0 : prev + 1
    );

    setTimeout(() => {
      setIsAnimating(false);
    }, 500);
  };

  useEffect(() => {
    const interval = setInterval(() => {
      goToNextSlide();
    }, 6000);

    return () => clearInterval(interval);
  }, [currentSlide, isAnimating]);

  return (
    <section
      id="testimonials"
      className="section-padding bg-primary text-white"
    >
      <div className="container">
        <div className="text-center mb-12">
          <h2 className="section-title centered mx-auto text-white">
            Client Testimonials
          </h2>
          <p className="max-w-2xl mx-auto text-white/80">
            Read what our satisfied clients have to say about their experience
            with Manya Mehendi Creations.
          </p>
        </div>

        <div className="relative max-w-4xl mx-auto">
          {/* Testimonial Slider */}
          <div className="overflow-hidden relative" ref={slideRef}>
            <div
              className="flex transition-transform duration-500 ease-in-out"
              style={{ transform: `translateX(-${currentSlide * 100}%)` }}
            >
              {testimonials.map((testimonial) => (
                <div key={testimonial.id} className="w-full flex-shrink-0 px-4">
                  <div className="bg-secondary/20 backdrop-blur-sm rounded-sm p-6 md:p-8">
                    <div className="flex flex-col md:flex-row md:items-center gap-6">
                      <div className="flex-shrink-0">
                        <div className="w-20 h-20 md:w-24 md:h-24 rounded-full overflow-hidden border-2 border-gold">
                          <Image
                            src={testimonial.image}
                            alt={testimonial.name}
                            width={120}
                            height={120}
                            className="object-cover w-full h-full"
                          />
                        </div>
                      </div>

                      <div className="flex-grow">
                        <div className="flex mb-2">
                          {[...Array(testimonial.rating)].map((_, i) => (
                            <Star
                              key={i}
                              size={16}
                              fill="#B7A179"
                              color="#B7A179"
                            />
                          ))}
                        </div>

                        <blockquote className="mb-4 italic text-white/90">
                          "{testimonial.content}"
                        </blockquote>

                        <div>
                          <h4 className="font-cormorant text-xl">
                            {testimonial.name}
                          </h4>
                          <p className="text-sm text-gold">
                            {testimonial.role}
                          </p>
                        </div>
                      </div>
                    </div>
                  </div>
                </div>
              ))}
            </div>
          </div>

          {/* Navigation Buttons */}
          <button
            onClick={goToPrevSlide}
            className="absolute top-1/2 -left-4 md:-left-12 transform -translate-y-1/2 w-10 h-10 rounded-full bg-white/10 flex items-center justify-center hover:bg-white/20 transition-colors"
            aria-label="Previous testimonial"
          >
            <ChevronLeft size={24} />
          </button>

          <button
            onClick={goToNextSlide}
            className="absolute top-1/2 -right-4 md:-right-12 transform -translate-y-1/2 w-10 h-10 rounded-full bg-white/10 flex items-center justify-center hover:bg-white/20 transition-colors"
            aria-label="Next testimonial"
          >
            <ChevronRight size={24} />
          </button>

          {/* Dots Indicator */}
          <div className="flex justify-center mt-8 space-x-2">
            {testimonials.map((_, index) => (
              <button
                key={index}
                onClick={() => setCurrentSlide(index)}
                className={`w-2 h-2 rounded-full transition-all ${
                  currentSlide === index ? "bg-gold w-8" : "bg-white/30"
                }`}
                aria-label={`Go to testimonial ${index + 1}`}
              />
            ))}
          </div>
        </div>
      </div>
    </section>
  );
}
