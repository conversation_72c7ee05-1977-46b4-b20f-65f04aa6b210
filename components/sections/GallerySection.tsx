import Image from "next/image";
import Link from "next/link";
import { SearchIcon } from "lucide-react";

interface GalleryItem {
  id: number;
  src: string;
  alt: string;
  category: string;
}

const galleryItems: GalleryItem[] = [
  {
    id: 1,
    src: "https://images.pexels.com/photos/13102907/pexels-photo-13102907.jpeg?auto=compress&cs=tinysrgb&w=1260&h=750&dpr=2",
    alt: "Intricate bridal mehendi design with traditional patterns",
    category: "Bridal",
  },
  {
    id: 2,
    src: "https://images.pexels.com/photos/17343894/pexels-photo-17343894.jpeg?auto=compress&cs=tinysrgb&w=1260&h=750&dpr=2",
    alt: "Elegant Arabic style mehendi with gold jewelry",
    category: "Arabic",
  },
  {
    id: 3,
    src: "https://images.pexels.com/photos/4723603/pexels-photo-4723603.jpeg?auto=compress&cs=tinysrgb&w=1260&h=750&dpr=2",
    alt: "Modern minimalist white henna design",
    category: "Contemporary",
  },
  {
    id: 4,
    src: "https://images.pexels.com/photos/28496968/pexels-photo-28496968.jpeg?auto=compress&cs=tinysrgb&w=1260&h=750&dpr=2",
    alt: "Traditional full hand mehendi with intricate details",
    category: "Traditional",
  },
  {
    id: 5,
    src: "https://images.pexels.com/photos/12872534/pexels-photo-12872534.jpeg?auto=compress&cs=tinysrgb&w=1260&h=750&dpr=2",
    alt: "Delicate finger mehendi with cultural motifs",
    category: "Minimal",
  },
  {
    id: 6,
    src: "https://images.pexels.com/photos/18380548/pexels-photo-18380548.jpeg?auto=compress&cs=tinysrgb&w=1260&h=750&dpr=2",
    alt: "Bold geometric mehendi pattern on bride's hand",
    category: "Geometric",
  },
];

export default function GallerySection() {
  return (
    <section
      id="gallery"
      className="section-padding bg-white"
      aria-labelledby="gallery-heading"
    >
      <div className="container">
        <div className="text-center mb-12">
          <h2 id="gallery-heading" className="section-title centered mx-auto">
            Mehendi Design Portfolio Gallery
          </h2>
          <p className="max-w-2xl mx-auto text-muted-foreground">
            Browse through a selection of our finest mehendi work, showcasing
            various styles from traditional bridal designs to contemporary henna
            patterns. Each design represents our commitment to intricate
            artistry and personalized beauty.
          </p>
        </div>

        <div
          className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 gap-6"
          role="grid"
          aria-label="Mehendi design gallery"
        >
          {galleryItems.map((item, index) => (
            <article
              key={item.id}
              className={`gallery-item rounded-sm overflow-hidden animate-on-scroll`}
              style={{ animationDelay: `${index * 100}ms` }}
              role="gridcell"
              aria-label={`${item.category} mehendi design`}
            >
              <div className="relative aspect-[4/5]">
                <Image
                  src={item.src}
                  alt={`${item.alt} - ${item.category} style mehendi by Manya Mehendi Creations`}
                  fill
                  className="object-cover transition-transform duration-300 hover:scale-105"
                  sizes="(max-width: 640px) 100vw, (max-width: 1024px) 50vw, 33vw"
                  quality={85}
                  loading="lazy"
                />
                <div className="gallery-overlay" aria-hidden="true">
                  <div className="p-4 text-center">
                    <span className="inline-block px-3 py-1 bg-gold/80 text-white text-xs uppercase tracking-wider mb-2">
                      {item.category}
                    </span>
                    <div className="w-10 h-10 bg-white/90 rounded-full flex items-center justify-center mx-auto mt-3">
                      <SearchIcon
                        className="h-5 w-5 text-primary"
                        aria-hidden="true"
                      />
                    </div>
                  </div>
                </div>
              </div>
            </article>
          ))}
        </div>

        <div className="text-center mt-10">
          <Link
            href="https://www.instagram.com/"
            target="_blank"
            rel="noopener noreferrer"
            className="btn btn-outline"
          >
            View More on Instagram
          </Link>
        </div>
      </div>
    </section>
  );
}
