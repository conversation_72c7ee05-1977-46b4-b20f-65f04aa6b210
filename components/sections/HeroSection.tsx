"use client";

import { useEffect, useState } from "react";
import OptimizedImage from "@/components/ui/OptimizedImage";
import { ChevronDown } from "lucide-react";

export default function HeroSection() {
  const [isLoaded, setIsLoaded] = useState(false);

  useEffect(() => {
    setIsLoaded(true);
  }, []);

  const scrollToAbout = () => {
    const aboutSection = document.querySelector("#about");
    if (aboutSection) {
      window.scrollTo({
        top: aboutSection.getBoundingClientRect().top + window.scrollY - 100,
        behavior: "smooth",
      });
    }
  };

  return (
    <section
      id="home"
      className="relative h-screen flex items-center justify-center overflow-hidden mehendi-pattern"
      role="banner"
      aria-label="Hero section showcasing Manya Mehendi Creations"
    >
      {/* Hero Background Image */}
      <div className="absolute inset-0 z-0">
        <OptimizedImage
          src="https://images.unsplash.com/photo-1566737236500-c8ac43014a8e?q=80&w=2940&auto=format&fit=crop&ixlib=rb-4.1.0&ixid=M3wxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8fA%3D%3D"
          alt="Beautiful bridal mehendi hands with intricate henna designs showcasing professional henna artistry"
          fill
          priority
          quality={85}
          sizes="100vw"
          placeholder="blur"
          blurDataURL="data:image/jpeg;base64,/9j/4AAQSkZJRgABAQAAAQABAAD/2wBDAAYEBQYFBAYGBQYHBwYIChAKCgkJChQODwwQFxQYGBcUFhYaHSUfGhsjHBYWICwgIyYnKSopGR8tMC0oMCUoKSj/2wBDAQcHBwoIChMKChMoGhYaKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCj/wAARCAAIAAoDASIAAhEBAxEB/8QAFQABAQAAAAAAAAAAAAAAAAAAAAv/xAAhEAACAQMDBQAAAAAAAAAAAAABAgMABAUGIWGRkqGx0f/EABUBAQEAAAAAAAAAAAAAAAAAAAMF/8QAGhEAAgIDAAAAAAAAAAAAAAAAAAECEgMRkf/aAAwDAQACEQMRAD8AltJagyeH0AthI5xdrLcNM91BF5pX2HaH9bcfaSXWGaRmknyJckliyjqTzSlT54b6bk+h0R//2Q=="
        />
        <div className="absolute inset-0 bg-black/60" aria-hidden="true"></div>
      </div>

      {/* Hero Content */}
      <div className="container relative z-10 text-center px-4">
        <div
          className={`transform transition-all duration-1000 ${
            isLoaded ? "translate-y-0 opacity-100" : "translate-y-10 opacity-0"
          }`}
        >
          <h1 className="text-white font-cormorant text-3xl md:text-5xl lg:text-7xl mb-6 font-medium drop-shadow-2xl">
            Professional Mehendi Artist in Delhi NCR
          </h1>
          <p className="text-white/90 max-w-2xl mx-auto mb-8 md:text-lg drop-shadow-xl">
            Transforming your special moments with intricate and personalized
            henna designs. Expert bridal mehendi, wedding henna, and bespoke
            artistry for celebrations and special occasions.
          </p>
          <div className="flex flex-col sm:flex-row justify-center gap-4">
            <a
              href="#contact"
              className="px-6 py-3 bg-yellow-500 hover:bg-yellow-600 text-black font-semibold rounded-lg shadow-lg transition duration-300 focus:outline-none focus:ring-2 focus:ring-yellow-400"
              aria-label="Book mehendi appointment"
            >
              Book Appointment
            </a>
            <a
              href="#gallery"
              className="px-6 py-3 border border-white/60 text-white hover:bg-white/10 font-semibold rounded-lg transition duration-300 focus:outline-none focus:ring-2 focus:ring-white/50"
              aria-label="View mehendi design gallery"
            >
              View Gallery
            </a>
          </div>
        </div>
      </div>

      {/* Scroll Down Indicator */}
      <button
        onClick={scrollToAbout}
        className={`absolute bottom-8 left-1/2 transform -translate-x-1/2 text-white transition-all duration-1000 z-10 animate-bounce ${
          isLoaded ? "opacity-100" : "opacity-0"
        }`}
        aria-label="Scroll down"
      >
        <ChevronDown size={36} />
      </button>
    </section>
  );
}
