"use client";

import { createContext, useContext, useEffect, useState, ReactNode } from 'react';

interface GSAPContextType {
  gsap: any;
  ScrollTrigger: any;
  isLoaded: boolean;
}

const GSAPContext = createContext<GSAPContextType>({
  gsap: null,
  ScrollTrigger: null,
  isLoaded: false,
});

export const useGSAP = () => {
  const context = useContext(GSAPContext);
  if (!context) {
    throw new Error('useGSAP must be used within a GSAPAnimationProvider');
  }
  return context;
};

interface GSAPAnimationProviderProps {
  children: ReactNode;
}

export default function GSAPAnimationProvider({ children }: GSAPAnimationProviderProps) {
  const [gsapLibs, setGsapLibs] = useState<GSAPContextType>({
    gsap: null,
    ScrollTrigger: null,
    isLoaded: false,
  });

  useEffect(() => {
    // Load GSAP dynamically after initial render to avoid blocking
    const loadGSAP = async () => {
      try {
        // Use dynamic imports to load GSAP only when needed
        const { gsap } = await import('gsap');
        const { ScrollTrigger } = await import('gsap/ScrollTrigger');
        
        // Register ScrollTrigger plugin
        gsap.registerPlugin(ScrollTrigger);
        
        setGsapLibs({
          gsap,
          ScrollTrigger,
          isLoaded: true,
        });
      } catch (error) {
        console.error('Failed to load GSAP:', error);
      }
    };

    // Load GSAP after a short delay to ensure critical content loads first
    const timer = setTimeout(loadGSAP, 100);
    
    return () => clearTimeout(timer);
  }, []);

  return (
    <GSAPContext.Provider value={gsapLibs}>
      {children}
    </GSAPContext.Provider>
  );
}

// Hook for creating GSAP animations with proper cleanup
export const useGSAPAnimation = (
  animationFn: (gsap: any, ScrollTrigger: any) => any,
  dependencies: any[] = []
) => {
  const { gsap, ScrollTrigger, isLoaded } = useGSAP();

  useEffect(() => {
    if (!isLoaded || !gsap || !ScrollTrigger) return;

    const animation = animationFn(gsap, ScrollTrigger);

    return () => {
      // Cleanup animations
      if (animation) {
        if (Array.isArray(animation)) {
          animation.forEach(anim => anim.kill?.());
        } else if (animation.kill) {
          animation.kill();
        }
      }
      
      // Refresh ScrollTrigger
      ScrollTrigger.refresh();
    };
  }, [isLoaded, gsap, ScrollTrigger, ...dependencies]);
};
