"use client";

import { useEffect } from 'react';

interface WebVital {
  name: string;
  value: number;
  rating: 'good' | 'needs-improvement' | 'poor';
}

export default function PerformanceMonitor() {
  useEffect(() => {
    // Only run in production and if the browser supports the APIs
    if (process.env.NODE_ENV !== 'production' || typeof window === 'undefined') {
      return;
    }

    const reportWebVital = (vital: WebVital) => {
      // Log to console in development
      if (process.env.NODE_ENV === 'development') {
        console.log(`${vital.name}: ${vital.value} (${vital.rating})`);
      }

      // Send to analytics service (replace with your analytics)
      if (typeof gtag !== 'undefined') {
        gtag('event', vital.name, {
          event_category: 'Web Vitals',
          value: Math.round(vital.value),
          event_label: vital.rating,
          non_interaction: true,
        });
      }
    };

    // Measure Core Web Vitals
    const measureWebVitals = async () => {
      try {
        const { getCLS, getFID, getFCP, getLCP, getTTFB } = await import('web-vitals');

        getCLS(reportWebVital);
        getFID(reportWebVital);
        getFCP(reportWebVital);
        getLCP(reportWebVital);
        getTTFB(reportWebVital);
      } catch (error) {
        console.warn('Failed to load web-vitals:', error);
      }
    };

    // Measure performance metrics
    const measurePerformance = () => {
      if ('performance' in window && 'getEntriesByType' in performance) {
        // Measure page load time
        const navigation = performance.getEntriesByType('navigation')[0] as PerformanceNavigationTiming;
        if (navigation) {
          const loadTime = navigation.loadEventEnd - navigation.fetchStart;
          console.log(`Page Load Time: ${loadTime}ms`);
        }

        // Measure resource loading times
        const resources = performance.getEntriesByType('resource') as PerformanceResourceTiming[];
        const slowResources = resources.filter(resource => resource.duration > 1000);
        if (slowResources.length > 0) {
          console.warn('Slow loading resources:', slowResources);
        }
      }
    };

    // Run measurements
    measureWebVitals();
    measurePerformance();

    // Monitor for layout shifts
    if ('PerformanceObserver' in window) {
      const observer = new PerformanceObserver((list) => {
        for (const entry of list.getEntries()) {
          if (entry.entryType === 'layout-shift' && !(entry as any).hadRecentInput) {
            console.log('Layout shift detected:', entry);
          }
        }
      });

      try {
        observer.observe({ entryTypes: ['layout-shift'] });
      } catch (e) {
        // Ignore if not supported
      }

      return () => observer.disconnect();
    }
  }, []);

  return null; // This component doesn't render anything
}

// Helper function to check if web vitals are good
export const getWebVitalRating = (name: string, value: number): 'good' | 'needs-improvement' | 'poor' => {
  switch (name) {
    case 'CLS':
      return value <= 0.1 ? 'good' : value <= 0.25 ? 'needs-improvement' : 'poor';
    case 'FID':
      return value <= 100 ? 'good' : value <= 300 ? 'needs-improvement' : 'poor';
    case 'LCP':
      return value <= 2500 ? 'good' : value <= 4000 ? 'needs-improvement' : 'poor';
    case 'FCP':
      return value <= 1800 ? 'good' : value <= 3000 ? 'needs-improvement' : 'poor';
    case 'TTFB':
      return value <= 800 ? 'good' : value <= 1800 ? 'needs-improvement' : 'poor';
    default:
      return 'good';
  }
};
