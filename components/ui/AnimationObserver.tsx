"use client";

import { useEffect, useRef, ReactNode, useCallback } from "react";

interface AnimationObserverProps {
  children: ReactNode;
}

export default function AnimationObserver({
  children,
}: AnimationObserverProps) {
  const observerRef = useRef<IntersectionObserver | null>(null);
  const observedElementsRef = useRef<Set<Element>>(new Set());

  const observeElements = useCallback(() => {
    if (!observerRef.current) return;

    // Find new elements to observe
    const animatedElements = document.querySelectorAll(".animate-on-scroll");
    animatedElements.forEach((el) => {
      if (!observedElementsRef.current.has(el)) {
        observerRef.current?.observe(el);
        observedElementsRef.current.add(el);
      }
    });
  }, []);

  useEffect(() => {
    // Setup intersection observer with optimized settings
    observerRef.current = new IntersectionObserver(
      (entries) => {
        entries.forEach((entry) => {
          if (entry.isIntersecting) {
            // Use requestAnimationFrame for smooth animations
            requestAnimationFrame(() => {
              entry.target.classList.add("is-visible");
            });

            // Unobserve after animation is triggered
            observerRef.current?.unobserve(entry.target);
            observedElementsRef.current.delete(entry.target);
          }
        });
      },
      {
        root: null,
        rootMargin: "50px 0px", // Start animation slightly before element is visible
        threshold: [0, 0.1, 0.25], // Multiple thresholds for better performance
      }
    );

    // Initial observation
    observeElements();

    // Re-observe when new elements are added (for dynamic content)
    const mutationObserver = new MutationObserver(() => {
      // Debounce the observation to avoid excessive calls
      setTimeout(observeElements, 100);
    });

    mutationObserver.observe(document.body, {
      childList: true,
      subtree: true,
    });

    // Cleanup
    return () => {
      if (observerRef.current) {
        observerRef.current.disconnect();
      }
      mutationObserver.disconnect();
      observedElementsRef.current.clear();
    };
  }, [observeElements]);

  return <>{children}</>;
}
